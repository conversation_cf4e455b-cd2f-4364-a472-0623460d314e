#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Python脚本反混淆工具
专门处理特定格式的混淆脚本
"""

import zlib
import base64
import lzma
import bz2
import gzip
import re

def extract_and_decode():
    """提取并解码混淆脚本中的数据"""

    # 读取原始文件
    with open('脚本_py3.11.py', 'r', encoding='utf-8') as f:
        content = f.read()

    print("正在分析混淆脚本...")

    # 从之前的输出中，我们知道第三个lambda字符串包含了主要数据
    # 让我们直接提取这个长字符串

    # 查找最长的base64字符串
    base64_pattern = r"'([A-Za-z0-9+/=]{1000,})'"
    matches = re.findall(base64_pattern, content)

    if not matches:
        print("未找到长base64字符串")
        return None

    # 取最长的字符串
    main_data = max(matches, key=len)
    print(f"找到主要数据，长度: {len(main_data)}")

    try:
        # 第一层：base64解码
        print("正在进行base64解码...")
        decoded = base64.b64decode(main_data)

        # 检测数据格式并尝试不同的解压顺序
        print(f"解码后数据前几个字节: {decoded[:10]}")

        # 尝试直接zlib解压
        try:
            print("尝试zlib解压...")
            result = zlib.decompress(decoded).decode('utf-8')
            print("zlib解压成功！")
            return result
        except:
            pass

        # 尝试gzip解压
        try:
            print("尝试gzip解压...")
            gzip_decompressed = gzip.decompress(decoded)
            print("gzip解压成功，继续处理...")

            # 继续多层解压
            bz2_decompressed = bz2.decompress(gzip_decompressed)
            lzma_decompressed = lzma.decompress(bz2_decompressed)
            final_decompressed = zlib.decompress(lzma_decompressed)

            result = final_decompressed.decode('utf-8')
            print("多层解压成功！")
            return result
        except Exception as e:
            print(f"gzip解压失败: {e}")

        # 尝试其他格式
        for decompress_func, name in [(bz2.decompress, 'bz2'), (lzma.decompress, 'lzma')]:
            try:
                print(f"尝试{name}解压...")
                result = decompress_func(decoded).decode('utf-8')
                print(f"{name}解压成功！")
                return result
            except:
                pass

        print("所有解压方法都失败了")
        return None

    except Exception as e:
        print(f"解压失败: {e}")
        return None

def save_deobfuscated_code(code):
    """保存反混淆后的代码"""
    if not code:
        print("没有代码可保存")
        return

    # 检查是否还需要进一步解码
    if code.startswith('H4sI'):
        print("检测到gzip压缩的base64数据，进行进一步解码...")
        try:
            # 这是gzip压缩的base64数据
            decoded = base64.b64decode(code)
            decompressed = gzip.decompress(decoded)
            code = decompressed.decode('utf-8')
            print("成功解压gzip数据！")
        except Exception as e:
            print(f"gzip解压失败: {e}")

    output_file = '脚本_py3.11_deobfuscated.py'

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(code)

    print(f"反混淆代码已保存到: {output_file}")
    print(f"代码长度: {len(code)} 字符")

    # 显示前几行预览
    lines = code.split('\n')
    print(f"\n代码预览 (前30行):")
    print("-" * 80)
    for i, line in enumerate(lines[:30], 1):
        print(f"{i:3d}: {line}")
    if len(lines) > 30:
        print(f"... 还有 {len(lines) - 30} 行")
    print("-" * 80)

def main():
    """主函数"""
    print("Python脚本反混淆工具")
    print("=" * 50)

    # 提取并解码
    deobfuscated_code = extract_and_decode()

    if deobfuscated_code:
        # 保存结果
        save_deobfuscated_code(deobfuscated_code)
        print("\n反混淆完成！")
    else:
        print("反混淆失败！")

if __name__ == "__main__":
    main()
