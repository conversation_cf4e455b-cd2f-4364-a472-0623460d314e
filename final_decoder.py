#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终解码器 - 处理gzip压缩的base64数据
"""

import base64
import gzip
import sys

def decode_final_layer():
    """解码最后一层数据"""

    # 读取之前生成的文件
    with open('脚本_py3.11_deobfuscated.py', 'r', encoding='utf-8') as f:
        encoded_data = f.read().strip()

    print(f"输入数据长度: {len(encoded_data)}")
    print(f"数据开头: {encoded_data[:50]}...")

    try:
        # 这是gzip压缩的base64数据
        print("正在进行base64解码...")
        decoded = base64.b64decode(encoded_data)
        print(f"解码后数据长度: {len(decoded)}")
        print(f"解码后数据前几个字节: {decoded[:20]}")

        # 检查是否是gzip格式 (以0x1f 0x8b开头)
        if decoded[:2] == b'\x1f\x8b':
            print("检测到gzip格式，正在解压...")
            decompressed = gzip.decompress(decoded)
            print(f"gzip解压后数据长度: {len(decompressed)}")
            print(f"解压后数据前几个字节: {decompressed[:20]}")

            # 尝试不同的编码方式
            for encoding in ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']:
                try:
                    result = decompressed.decode(encoding)
                    print(f"使用 {encoding} 编码成功！")
                    return result
                except UnicodeDecodeError:
                    print(f"{encoding} 编码失败，尝试下一个...")
                    continue

            # 如果所有编码都失败，尝试忽略错误
            try:
                result = decompressed.decode('utf-8', errors='ignore')
                print("使用UTF-8编码（忽略错误）成功！")
                return result
            except:
                print("所有解码方法都失败")
                return None
        else:
            print("不是gzip格式，尝试直接解码为UTF-8...")
            result = decoded.decode('utf-8')
            return result

    except Exception as e:
        print(f"解码失败: {e}")
        return None

def save_final_result(code):
    """保存最终结果"""
    if not code:
        print("没有代码可保存")
        return

    output_file = '脚本_py3.11_final.py'

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(code)

    print(f"最终代码已保存到: {output_file}")
    print(f"代码长度: {len(code)} 字符")

    # 显示前几行预览
    lines = code.split('\n')
    print(f"\n最终代码预览 (前50行):")
    print("=" * 80)
    for i, line in enumerate(lines[:50], 1):
        print(f"{i:3d}: {line}")
    if len(lines) > 50:
        print(f"... 还有 {len(lines) - 50} 行")
    print("=" * 80)

def main():
    """主函数"""
    print("最终解码器")
    print("=" * 50)

    # 解码最后一层
    final_code = decode_final_layer()

    if final_code:
        # 保存结果
        save_final_result(final_code)
        print("\n最终解码完成！")
    else:
        print("最终解码失败！")

if __name__ == "__main__":
    main()
