#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python脚本反混淆工具
用于解密和反混淆经过多层编码的Python脚本
"""

import zlib
import base64
import lzma
import bz2
import gzip
import re
import ast
import sys

def decode_base64_zlib(encoded_str):
    """解码base64并解压zlib"""
    try:
        decoded = base64.b64decode(encoded_str)
        decompressed = zlib.decompress(decoded)
        return decompressed.decode('utf-8')
    except Exception as e:
        print(f"解码失败: {e}")
        return None

def extract_lambda_strings(code):
    """提取lambda函数中的字符串"""
    # 查找lambda函数中的base64字符串
    pattern = r"lambda s:zlib\.decompress\(base64\.b64decode\(s\)\)\.decode\(\)\)\('([^']+)'\)"
    matches = re.findall(pattern, code)
    return matches

def decompress_multilayer(encoded_data):
    """多层解压缩"""
    try:
        # 第一层：base64解码
        print("正在进行base64解码...")
        decoded = base64.b64decode(encoded_data)

        # 第二层：gzip解压
        print("正在进行gzip解压...")
        gzip_decompressed = gzip.decompress(decoded)

        # 第三层：bz2解压
        print("正在进行bz2解压...")
        bz2_decompressed = bz2.decompress(gzip_decompressed)

        # 第四层：lzma解压
        print("正在进行lzma解压...")
        lzma_decompressed = lzma.decompress(bz2_decompressed)

        # 第五层：zlib解压
        print("正在进行zlib解压...")
        final_decompressed = zlib.decompress(lzma_decompressed)

        return final_decompressed.decode('utf-8')
    except Exception as e:
        print(f"多层解压失败: {e}")
        return None

def deobfuscate_script(file_path):
    """反混淆脚本主函数"""
    print(f"正在分析文件: {file_path}")

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    print("原始混淆代码结构:")
    print("=" * 50)
    print(content[:200] + "..." if len(content) > 200 else content)
    print("=" * 50)

    # 提取lambda函数中的字符串
    lambda_strings = extract_lambda_strings(content)
    print(f"\n找到 {len(lambda_strings)} 个lambda字符串")

    for i, s in enumerate(lambda_strings):
        decoded = decode_base64_zlib(s)
        if decoded:
            print(f"Lambda字符串 {i+1}: {decoded}")

    # 查找主要的base64编码数据 - 更广泛的模式匹配
    base64_patterns = [
        r"base64\.b64decode\('([A-Za-z0-9+/=]+)'\)",
        r"base64\.b64decode\(([A-Za-z0-9+/=]+)\)",
        r"'([A-Za-z0-9+/=]{100,})'"  # 查找长的base64字符串
    ]

    all_base64_matches = []
    for pattern in base64_patterns:
        matches = re.findall(pattern, content)
        all_base64_matches.extend(matches)

    if all_base64_matches:
        # 通常最长的字符串包含主要内容
        main_encoded = max(all_base64_matches, key=len)
        print(f"\n找到主要编码数据，长度: {len(main_encoded)}")

        # 尝试多层解压
        decompressed = decompress_multilayer(main_encoded)

        if decompressed:
            print("\n成功解压！正在分析解压后的代码...")
            return decompressed
        else:
            print("解压失败")
            return None
    else:
        print("未找到base64编码数据")
        # 尝试直接执行并捕获结果
        print("尝试动态分析...")
        return dynamic_analysis(content)

def dynamic_analysis(content):
    """动态分析混淆代码"""
    try:
        print("正在进行动态分析...")

        # 创建一个安全的执行环境
        import io
        import contextlib

        # 重定向stdout来捕获输出
        captured_output = io.StringIO()

        # 创建受限的全局命名空间
        safe_globals = {
            '__builtins__': {
                'compile': compile,
                'exec': exec,
                'print': print,
                'len': len,
                'str': str,
                'bytes': bytes,
                'int': int,
                'float': float,
                'list': list,
                'dict': dict,
                'tuple': tuple,
                'set': set,
            },
            'zlib': zlib,
            'base64': base64,
            'lzma': lzma,
            'bz2': bz2,
            'gzip': gzip,
        }

        # 修改exec函数来捕获执行的代码
        original_exec = exec
        executed_code = []

        def capture_exec(code, globals_dict=None, locals_dict=None):
            if isinstance(code, str):
                executed_code.append(code)
                print(f"捕获到执行的代码: {code[:100]}...")
            return original_exec(code, globals_dict, locals_dict)

        safe_globals['__builtins__']['exec'] = capture_exec

        # 尝试执行代码
        with contextlib.redirect_stdout(captured_output):
            exec(content, safe_globals)

        # 检查是否捕获到了代码
        if executed_code:
            print(f"成功捕获到 {len(executed_code)} 段代码")
            # 返回最后执行的代码（通常是反混淆后的代码）
            return executed_code[-1]

        # 检查输出
        output = captured_output.getvalue()
        if output:
            print(f"捕获到输出: {output[:200]}...")
            return output

        return None

    except Exception as e:
        print(f"动态分析失败: {e}")
        return None

def format_python_code(code):
    """格式化Python代码"""
    try:
        # 尝试解析AST来验证代码有效性
        ast.parse(code)

        # 简单的代码格式化
        lines = code.split('\n')
        formatted_lines = []
        indent_level = 0

        for line in lines:
            stripped = line.strip()
            if not stripped:
                formatted_lines.append('')
                continue

            # 减少缩进
            if stripped.startswith(('except', 'elif', 'else', 'finally')):
                indent_level = max(0, indent_level - 1)
            elif stripped.startswith(('def ', 'class ', 'if ', 'for ', 'while ', 'try:', 'with ')):
                pass
            elif stripped.endswith(':'):
                pass
            else:
                if indent_level > 0 and not stripped.startswith(('def ', 'class ', 'if ', 'for ', 'while ', 'try:', 'with ', 'except', 'elif', 'else', 'finally')):
                    pass

            formatted_lines.append('    ' * indent_level + stripped)

            # 增加缩进
            if stripped.endswith(':'):
                indent_level += 1

        return '\n'.join(formatted_lines)
    except SyntaxError:
        # 如果代码有语法错误，返回原始代码
        return code

def main():
    if len(sys.argv) != 2:
        print("用法: python deobfuscator.py <混淆脚本文件>")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = input_file.replace('.py', '_deobfuscated.py')

    try:
        deobfuscated_code = deobfuscate_script(input_file)

        if deobfuscated_code:
            # 格式化代码
            formatted_code = format_python_code(deobfuscated_code)

            # 保存到文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(formatted_code)

            print(f"\n反混淆完成！")
            print(f"输出文件: {output_file}")
            print(f"代码长度: {len(formatted_code)} 字符")

            # 显示前几行代码预览
            lines = formatted_code.split('\n')
            print(f"\n代码预览 (前20行):")
            print("-" * 50)
            for i, line in enumerate(lines[:20], 1):
                print(f"{i:3d}: {line}")
            if len(lines) > 20:
                print(f"... 还有 {len(lines) - 20} 行")
            print("-" * 50)

        else:
            print("反混淆失败")
            sys.exit(1)

    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
