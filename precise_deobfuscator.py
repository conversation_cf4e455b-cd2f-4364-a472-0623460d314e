#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确的Python脚本反混淆工具
专门处理这种特定格式的混淆脚本
"""

import zlib
import base64
import lzma
import bz2
import gzip

def decode_lambda_string(encoded_str):
    """解码lambda函数中的字符串"""
    try:
        decoded = base64.b64decode(encoded_str)
        decompressed = zlib.decompress(decoded)
        return decompressed.decode('utf-8')
    except Exception as e:
        print(f"Lambda解码失败: {e}")
        return None

def extract_main_data():
    """提取主要的编码数据"""

    # 读取原始文件
    with open('脚本_py3.11.py', 'r', encoding='utf-8') as f:
        content = f.read()

    print("正在分析脚本结构...")

    # 从脚本中我们可以看到有三个lambda函数调用
    # 第一个: 'eJyzKS4pysxLtwMADfkDEg=='
    # 第二个: 'eJxLrUhNBgAELQGm'
    # 第三个: 很长的base64字符串

    # 解码前两个lambda字符串
    lambda1 = decode_lambda_string('eJyzKS4pysxLtwMADfkDEg==')
    lambda2 = decode_lambda_string('eJxLrUhNBgAELQGm')

    print(f"Lambda 1 解码结果: {lambda1}")
    print(f"Lambda 2 解码结果: {lambda2}")

    # 查找最长的base64字符串
    # 从脚本结构看，主要数据在最后的长字符串中
    import re

    # 查找所有被单引号包围的长字符串
    pattern = r"'([A-Za-z0-9+/=]{1000,})'"
    matches = re.findall(pattern, content)

    if not matches:
        print("未找到长base64字符串")
        return None

    # 取最长的字符串
    main_encoded = max(matches, key=len)
    print(f"找到主要编码数据，长度: {len(main_encoded)}")

    # 首先通过lambda解码
    lambda_decoded = decode_lambda_string(main_encoded)
    if not lambda_decoded:
        print("Lambda解码失败")
        return None

    print(f"Lambda解码后长度: {len(lambda_decoded)}")

    # 现在进行多层解压：zlib.decompress(lzma.decompress(bz2.decompress(gzip.decompress(base64.b64decode(...)))))
    try:
        # 第一层：base64解码
        print("正在进行base64解码...")
        decoded = base64.b64decode(lambda_decoded)

        # 第二层：gzip解压
        print("正在进行gzip解压...")
        gzip_decompressed = gzip.decompress(decoded)

        # 第三层：bz2解压
        print("正在进行bz2解压...")
        bz2_decompressed = bz2.decompress(gzip_decompressed)

        # 第四层：lzma解压
        print("正在进行lzma解压...")
        lzma_decompressed = lzma.decompress(bz2_decompressed)

        # 第五层：zlib解压
        print("正在进行zlib解压...")
        final_decompressed = zlib.decompress(lzma_decompressed)

        # 解码为字符串
        result = final_decompressed.decode('utf-8')
        print("多层解压成功！")

        return result

    except Exception as e:
        print(f"多层解压失败: {e}")
        return None

def save_deobfuscated_code(code):
    """保存反混淆后的代码"""
    if not code:
        print("没有代码可保存")
        return

    output_file = '脚本_py3.11_final_deobfuscated.py'

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(code)

    print(f"反混淆代码已保存到: {output_file}")
    print(f"代码长度: {len(code)} 字符")

    # 显示前几行预览
    lines = code.split('\n')
    print(f"\n代码预览 (前50行):")
    print("=" * 80)
    for i, line in enumerate(lines[:50], 1):
        print(f"{i:3d}: {line}")
    if len(lines) > 50:
        print(f"... 还有 {len(lines) - 50} 行")
    print("=" * 80)

def main():
    """主函数"""
    print("精确Python脚本反混淆工具")
    print("=" * 50)

    # 提取并解码
    deobfuscated_code = extract_main_data()

    if deobfuscated_code:
        # 保存结果
        save_deobfuscated_code(deobfuscated_code)
        print("\n反混淆完成！")
    else:
        print("反混淆失败！")

if __name__ == "__main__":
    main()
