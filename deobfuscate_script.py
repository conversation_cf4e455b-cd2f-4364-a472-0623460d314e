#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本解混淆工具
用于分析和解密混淆的Python脚本
"""

import re
import unicodedata
import codecs

def analyze_obfuscated_script(file_path):
    """分析混淆脚本的结构"""
    print(f"正在分析文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"文件大小: {len(content)} 字符")
    print(f"行数: {content.count(chr(10)) + 1}")
    
    # 分析字符类型
    unicode_chars = []
    normal_chars = []
    
    for char in content:
        if ord(char) > 127:
            unicode_chars.append(char)
        else:
            normal_chars.append(char)
    
    print(f"Unicode字符数量: {len(unicode_chars)}")
    print(f"普通ASCII字符数量: {len(normal_chars)}")
    
    # 查找可能的变量名
    var_pattern = r"[a-zA-Z_][a-zA-Z0-9_]*\s*="
    variables = re.findall(var_pattern, content)
    print(f"发现的变量: {variables[:10]}")  # 只显示前10个
    
    return content

def remove_unicode_modifiers(text):
    """移除Unicode修饰符"""
    # 移除组合字符和修饰符
    normalized = unicodedata.normalize('NFD', text)
    ascii_text = ''.join(c for c in normalized if unicodedata.category(c) != 'Mn')
    return ascii_text

def extract_meaningful_content(content):
    """提取有意义的内容"""
    # 移除Unicode修饰符
    clean_content = remove_unicode_modifiers(content)
    
    # 查找Python代码模式
    patterns = [
        r'import\s+\w+',
        r'from\s+\w+\s+import',
        r'def\s+\w+\s*\(',
        r'class\s+\w+\s*\(',
        r'if\s+__name__\s*==\s*["\']__main__["\']',
        r'print\s*\(',
        r'exec\s*\(',
        r'eval\s*\(',
    ]
    
    found_code = []
    for pattern in patterns:
        matches = re.findall(pattern, clean_content, re.IGNORECASE)
        found_code.extend(matches)
    
    return found_code

def decode_obfuscated_strings(content):
    """尝试解码混淆的字符串"""
    # 查找可能的编码字符串
    string_patterns = [
        r'["\']([^"\']+)["\']',
        r'b["\']([^"\']+)["\']',
        r'r["\']([^"\']+)["\']',
    ]
    
    decoded_strings = []
    
    for pattern in string_patterns:
        matches = re.findall(pattern, content)
        for match in matches:
            try:
                # 尝试各种解码方式
                decodings = [
                    match,
                    codecs.decode(match, 'unicode_escape'),
                    match.encode().decode('unicode_escape'),
                ]
                
                for decoded in decodings:
                    if decoded and decoded.isprintable() and len(decoded) > 3:
                        decoded_strings.append(decoded)
            except:
                continue
    
    return list(set(decoded_strings))

def main():
    file_path = "jiaoben_deobfuscated_deobfuscated_deobfuscated.py"
    
    try:
        # 分析脚本结构
        content = analyze_obfuscated_script(file_path)
        
        print("\n" + "="*50)
        print("开始解混淆分析...")
        
        # 提取有意义的代码
        meaningful_code = extract_meaningful_content(content)
        if meaningful_code:
            print(f"\n发现的代码模式:")
            for code in meaningful_code[:20]:  # 限制输出
                print(f"  {code}")
        
        # 解码字符串
        decoded_strings = decode_obfuscated_strings(content)
        if decoded_strings:
            print(f"\n解码的字符串:")
            for string in decoded_strings[:20]:  # 限制输出
                print(f"  {repr(string)}")
        
        # 查找可能的执行代码
        exec_patterns = [
            r'exec\s*\([^)]+\)',
            r'eval\s*\([^)]+\)',
            r'compile\s*\([^)]+\)',
        ]
        
        exec_code = []
        for pattern in exec_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            exec_code.extend(matches)
        
        if exec_code:
            print(f"\n发现的执行代码:")
            for code in exec_code[:10]:
                print(f"  {code[:100]}...")
        
        # 尝试提取base64编码的内容
        import base64
        b64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
        b64_matches = re.findall(b64_pattern, content)
        
        print(f"\n发现的可能base64编码: {len(b64_matches)} 个")
        
        for i, match in enumerate(b64_matches[:5]):  # 只检查前5个
            try:
                decoded = base64.b64decode(match).decode('utf-8')
                if decoded.isprintable():
                    print(f"  Base64解码 {i+1}: {decoded[:100]}...")
            except:
                continue
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")

if __name__ == "__main__":
    main()
