#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unicode混淆脚本反混淆工具
专门处理使用Unicode字符混淆的Python脚本
"""

import re
import unicodedata
import base64
import zlib
import sys

def clean_unicode_obfuscation(text):
    """清理Unicode混淆字符"""
    # 移除所有Unicode组合字符和修饰符
    cleaned = ''.join(c for c in text if unicodedata.category(c) not in ['Mn', 'Mc', 'Me'])
    
    # 移除零宽字符
    zero_width_chars = ['\u200b', '\u200c', '\u200d', '\u2060', '\ufeff']
    for char in zero_width_chars:
        cleaned = cleaned.replace(char, '')
    
    return cleaned

def extract_hidden_code(text):
    """提取隐藏在Unicode字符中的代码"""
    # 查找可能的Python代码模式
    patterns = [
        r"exec\s*\([^)]+\)",
        r"eval\s*\([^)]+\)",
        r"compile\s*\([^)]+\)",
        r"base64\.b64decode\s*\([^)]+\)",
        r"zlib\.decompress\s*\([^)]+\)",
        r"'([A-Za-z0-9+/=]{50,})'",  # Base64字符串
    ]
    
    found_code = []
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        found_code.extend(matches)
    
    return found_code

def decode_base64_strings(text):
    """解码文本中的base64字符串"""
    # 查找base64字符串
    base64_pattern = r"'([A-Za-z0-9+/=]{50,})'"
    matches = re.findall(base64_pattern, text)
    
    decoded_strings = []
    for match in matches:
        try:
            decoded = base64.b64decode(match)
            # 尝试zlib解压
            try:
                decompressed = zlib.decompress(decoded)
                decoded_strings.append(decompressed.decode('utf-8'))
            except:
                # 如果不是zlib压缩，直接解码
                try:
                    decoded_strings.append(decoded.decode('utf-8'))
                except:
                    decoded_strings.append(f"Binary data: {len(decoded)} bytes")
        except Exception as e:
            print(f"解码base64失败: {e}")
    
    return decoded_strings

def analyze_obfuscated_file(file_path):
    """分析混淆文件"""
    print(f"正在分析文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"原始文件大小: {len(content)} 字符")
    
    # 清理Unicode混淆
    cleaned_content = clean_unicode_obfuscation(content)
    print(f"清理后大小: {len(cleaned_content)} 字符")
    
    # 提取隐藏代码
    hidden_code = extract_hidden_code(cleaned_content)
    print(f"找到 {len(hidden_code)} 个代码片段")
    
    # 解码base64字符串
    decoded_strings = decode_base64_strings(cleaned_content)
    print(f"解码了 {len(decoded_strings)} 个base64字符串")
    
    return cleaned_content, hidden_code, decoded_strings

def save_results(original_file, cleaned_content, hidden_code, decoded_strings):
    """保存分析结果"""
    base_name = original_file.replace('.py', '')
    
    # 保存清理后的内容
    cleaned_file = f"{base_name}_cleaned.py"
    with open(cleaned_file, 'w', encoding='utf-8') as f:
        f.write(cleaned_content)
    print(f"清理后的内容保存到: {cleaned_file}")
    
    # 保存隐藏代码
    if hidden_code:
        code_file = f"{base_name}_hidden_code.txt"
        with open(code_file, 'w', encoding='utf-8') as f:
            for i, code in enumerate(hidden_code, 1):
                f.write(f"=== 代码片段 {i} ===\n")
                f.write(code)
                f.write("\n\n")
        print(f"隐藏代码保存到: {code_file}")
    
    # 保存解码字符串
    if decoded_strings:
        decoded_file = f"{base_name}_decoded.txt"
        with open(decoded_file, 'w', encoding='utf-8') as f:
            for i, decoded in enumerate(decoded_strings, 1):
                f.write(f"=== 解码字符串 {i} ===\n")
                f.write(decoded)
                f.write("\n\n")
        print(f"解码字符串保存到: {decoded_file}")
        
        # 如果解码的字符串看起来像Python代码，单独保存
        for i, decoded in enumerate(decoded_strings, 1):
            if any(keyword in decoded for keyword in ['import', 'def ', 'class ', 'if ', 'for ', 'while ']):
                python_file = f"{base_name}_decoded_code_{i}.py"
                with open(python_file, 'w', encoding='utf-8') as f:
                    f.write(decoded)
                print(f"Python代码保存到: {python_file}")

def main():
    if len(sys.argv) != 2:
        print("用法: python unicode_deobfuscator.py <混淆脚本文件>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    
    try:
        cleaned_content, hidden_code, decoded_strings = analyze_obfuscated_file(input_file)
        save_results(input_file, cleaned_content, hidden_code, decoded_strings)
        
        print("\n=== 分析完成 ===")
        print("请检查生成的文件以查看反混淆结果")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
